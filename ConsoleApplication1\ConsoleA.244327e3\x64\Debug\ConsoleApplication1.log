﻿  main.cpp
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(82,5): error C2059: 语法错误:“if”
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(84,5): error C2059: 语法错误:“return”
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(85,1): error C2059: 语法错误:“}”
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(85,1): error C2143: 语法错误: 缺少“;”(在“}”的前面)
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(88,30): error C2143: 语法错误: 缺少“;”(在“{”的前面)
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(88,30): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(93,20): error C2065: “max_batch_mem”: 未声明的标识符
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(93,40): error C2065: “samples_left”: 未声明的标识符
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(98,44): error C2065: “k”: 未声明的标识符
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(98,48): error C2065: “remaining_time”: 未声明的标识符
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(99,5): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(99,5): error C2371: “theoretical_max”: 重定义；不同的基类型
      C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(98,15):
      参见“theoretical_max”的声明
  
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(100,5): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(100,5): error C2374: “high”: 重定义；多次初始化
      C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(93,9):
      参见“high”的声明
  
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(100,5): error C2086: “int high”: 重定义
      C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(93,9):
      参见“high”的声明
  
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(102,5): error C2059: 语法错误:“while”
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(102,25): error C2143: 语法错误: 缺少“;”(在“{”的前面)
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(102,25): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(116,5): error C2059: 语法错误:“return”
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(117,1): error C2059: 语法错误:“}”
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(117,1): error C2143: 语法错误: 缺少“;”(在“}”的前面)
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(122,12): error C2143: 语法错误: 缺少“;”(在“{”的前面)
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(122,12): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)

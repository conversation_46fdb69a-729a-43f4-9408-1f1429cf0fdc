#include <iostream>
#include <vector>
#include <queue>
#include <cmath>
#include <algorithm>
#include <numeric>
#include <tuple>
#include <climits> // For INT_MAX

using namespace std;

// Simplified move penalty - use integer for better performance
const int MOVE_PENALTY = 50;

// --- Data Models ---

struct Server {
    int id;
    int g; // NPU count
    int k; // Speed coefficient
    int m; // Memory size
};

struct NPU {
    int id;
    int server_id;
    int k;
    int m;
    // List of (time, memory_delta) events, kept sorted by time
    vector<pair<int, int>> events;

    void add_task(int start_time, int finish_time, int mem_needed) {
        // Inserts events while maintaining sort order by time.
        // lower_bound finds the first element not less than the given value.
        auto it_start = lower_bound(events.begin(), events.end(), make_pair(start_time, INT_MIN));
        events.insert(it_start, {start_time, mem_needed});

        auto it_finish = lower_bound(events.begin(), events.end(), make_pair(finish_time, INT_MIN));
        events.insert(it_finish, {finish_time, -mem_needed});
    }

    int find_earliest_start_time(int arrival_time, int mem_needed) const {
        if (mem_needed > m) {
            return -1; // Using -1 to represent infinity/failure
        }

        int current_mem_usage = 0; // Changed from long long to int
        for (const auto& event : events) {
            if (event.first <= arrival_time) {
                current_mem_usage += event.second;
            } else {
                break;
            }
        }

        if (current_mem_usage + mem_needed <= m) {
            return arrival_time;
        }

        // If not enough memory, check future event points for released memory
        // Find the first event that happens after arrival_time
        auto it = upper_bound(events.begin(), events.end(), make_pair(arrival_time, INT_MAX));

        for (auto i = it; i != events.end(); ++i) {
            current_mem_usage += i->second;
            if (current_mem_usage + mem_needed <= m) {
                return i->first; // Found a slot starting at this event's time
            }
        }
        return -1; // No suitable slot found
    }
};

struct User {
    int id;
    int s, e, cnt;
    long long samples_left;
    long long next_send_time;
    int last_npu_id = -1;
    // (time, server_id, npu_id_in_server, batch_size)
    vector<tuple<int, int, int, int>> requests;
};


// --- Helper Functions ---

// Calculate correct inference time according to the problem statement
// Formula: ceil(Bj / f(Bj)) = ceil(Bj / (ki * sqrt(Bj))) = ceil(sqrt(Bj) / ki)
int calculate_inference_time(int batch_size, int k) {
    if (batch_size <= 0) return 0;
    double sqrt_batch = sqrt(batch_size);
    return static_cast<int>(ceil(sqrt_batch / k));
}

// Improved batch size selection with better time awareness
int find_good_batch_size(int max_batch, int samples_left, int k, long long remaining_time) {
    int limit = min(max_batch, (int)samples_left);
    if (limit <= 0) return 1;

    // Generate more intelligent candidates based on time constraints
    vector<int> candidates;
    candidates.push_back(1);                    // Minimum
    candidates.push_back(limit);                // Maximum

    // Add geometric progression for better coverage
    for (int batch = 1; batch <= limit; batch *= 2) {
        candidates.push_back(batch);
    }

    // Add candidates based on time constraints
    if (remaining_time > 0) {
        // Try to find batch sizes that fit well within time window
        for (int batch = 1; batch <= limit; batch += max(1, limit / 10)) {
            int inference_time = calculate_inference_time(batch, k);
            if (inference_time <= remaining_time) {
                candidates.push_back(batch);
            }
        }
    }

    // Remove duplicates and sort
    sort(candidates.begin(), candidates.end());
    candidates.erase(unique(candidates.begin(), candidates.end()), candidates.end());

    int best_batch = 1;
    double best_score = -1;

    for (int batch : candidates) {
        if (batch <= 0 || batch > limit) continue;

        int inference_time = calculate_inference_time(batch, k);

        // More sophisticated scoring function
        double efficiency = (double)batch / inference_time; // Throughput
        double time_penalty = 1.0;

        if (remaining_time > 0) {
            if (inference_time > remaining_time) {
                time_penalty = 0.1; // Heavy penalty for exceeding time
            } else {
                // Prefer batches that use time efficiently
                time_penalty = 1.0 + (double)(remaining_time - inference_time) / remaining_time * 0.5;
            }
        }

        double score = efficiency * time_penalty;

        if (score > best_score) {
            best_score = score;
            best_batch = batch;
        }
    }

    return best_batch;
}

// --- Main Logic ---

int main() {
    // Fast I/O
    ios_base::sync_with_stdio(false);
    cin.tie(NULL);

    // 1. Read Input
    int N;
    cin >> N;
    if (cin.eof()) return 0;

    vector<Server> servers_data(N);
    for (int i = 0; i < N; ++i) {
        servers_data[i].id = i;
        cin >> servers_data[i].g >> servers_data[i].k >> servers_data[i].m;
    }

    int M;
    cin >> M;
    vector<User> users(M);
    for (int i = 0; i < M; ++i) {
        users[i].id = i;
        cin >> users[i].s >> users[i].e >> users[i].cnt;
        users[i].samples_left = users[i].cnt;
        users[i].next_send_time = users[i].s;
    }

    vector<vector<int>> latencies(N, vector<int>(M));
    for (int i = 0; i < N; ++i) {
        for (int j = 0; j < M; ++j) {
            cin >> latencies[i][j];
        }
    }

    int A, B;
    cin >> A >> B;

    // 2. Initialize Models
    vector<NPU> npus;
    int npu_counter = 0;
    for (const auto& server : servers_data) {
        for (int i = 0; i < server.g; ++i) {
            npus.push_back({npu_counter++, server.id, server.k, server.m, {}});
        }
    }

    // 3. Event-driven Scheduling with improved prioritization
    // Priority considers both time urgency and remaining work
    priority_queue<pair<double, int>> user_pq;
    for (const auto& user : users) {
        // Calculate urgency: consider time window tightness and remaining samples
        double time_window = user.e - user.s;
        double urgency = (double)user.cnt / time_window; // samples per time unit
        double time_factor = 1.0 / (user.next_send_time + 1); // Earlier is more urgent
        double priority = -(urgency * time_factor); // Negative for min-heap behavior
        user_pq.push({priority, user.id});
    }

    while (!user_pq.empty()) {
        auto top = user_pq.top();
        user_pq.pop();
        int user_id = top.second;
        User& user = users[user_id];

        if (user.samples_left <= 0) continue;

        long long send_time = user.next_send_time;

        // --- Decision Making with improved cost function ---
        double best_cost = -1;
        int best_finish_time = -1;
        int best_npu_idx = -1;
        int best_batch_size = -1;

        // Calculate user's remaining time and urgency
        long long remaining_time = max(0LL, (long long)user.e - send_time);
        double urgency_factor = 1.0;
        if (remaining_time > 0) {
            double time_pressure = (double)user.samples_left / remaining_time;
            urgency_factor = 1.0 + time_pressure; // Higher pressure = higher urgency
        }

        for (int i = 0; i < npus.size(); ++i) {
            const auto& npu = npus[i];
            const auto& server = servers_data[npu.server_id];

            if (server.m <= B) continue;
            int max_b_for_npu = (server.m - B) / A;
            if (max_b_for_npu <= 0) continue;

            // Use improved batch size selection
            int good_batch = find_good_batch_size(max_b_for_npu, user.samples_left, server.k, remaining_time);
            int mem_needed = A * good_batch + B;

            int latency = latencies[server.id][user.id];
            int arrival_time = send_time + latency;

            int start_time = npu.find_earliest_start_time(arrival_time, mem_needed);
            if (start_time == -1) continue;

            // Use correct inference time calculation
            int inference_time = calculate_inference_time(good_batch, server.k);
            int finish_time = start_time + inference_time;

            // Improved cost function considering multiple factors
            double cost = finish_time * urgency_factor;

            // Migration penalty
            if (user.last_npu_id != -1 && npu.id != user.last_npu_id) {
                cost += MOVE_PENALTY * urgency_factor;
            }

            // Heavy penalty for exceeding deadline
            if (finish_time > user.e) {
                double overtime = finish_time - user.e;
                cost += overtime * overtime * 10.0; // Quadratic penalty for overtime
            }

            // Bonus for efficient resource utilization
            double efficiency = (double)good_batch / inference_time;
            cost -= efficiency * 5.0; // Small bonus for efficiency

            // Prefer NPUs with lower latency
            cost += latency * 0.5;

            if (best_cost == -1 || cost < best_cost) {
                best_cost = cost;
                best_finish_time = finish_time;
                best_npu_idx = i;
                best_batch_size = good_batch;
            }
        }

        // --- Commit to the best decision ---
        if (best_npu_idx != -1) {
            NPU& chosen_npu = npus[best_npu_idx];
            int batch = best_batch_size;

            int mem_needed = A * batch + B;
            int latency = latencies[chosen_npu.server_id][user.id];
            int server_k = servers_data[chosen_npu.server_id].k;
            int inference_time = calculate_inference_time(batch, server_k); // Use correct calculation
            int start_time = best_finish_time - inference_time;

            chosen_npu.add_task(start_time, best_finish_time, mem_needed);

            int npu_id_in_server;
            int base_npu_id = 0;
            for(int i = 0; i < chosen_npu.server_id; ++i) {
                base_npu_id += servers_data[i].g;
            }
            npu_id_in_server = chosen_npu.id - base_npu_id + 1;

            user.requests.emplace_back(send_time, chosen_npu.server_id + 1, npu_id_in_server, batch);

            user.samples_left -= batch;
            user.next_send_time = send_time + latency + 1;
            user.last_npu_id = chosen_npu.id;

            if (user.samples_left > 0) {
                // Update priority considering new state
                double time_window = user.e - user.s;
                double urgency = (double)user.samples_left / time_window;
                double time_factor = 1.0 / (user.next_send_time + 1);
                double priority = -(urgency * time_factor);
                user_pq.push({priority, user.id});
            }
        }
    }

    // 4. Print Output
    for (auto& user : users) {
        sort(user.requests.begin(), user.requests.end());
        cout << user.requests.size() << "\n";
        bool first = true;
        for (const auto& req : user.requests) {
            if (!first) {
                cout << " ";
            }
            cout << get<0>(req) << " " << get<1>(req) << " "
                 << get<2>(req) << " " << get<3>(req);
            first = false;
        }
        cout << "\n";
    }

    return 0;
} 
#include <iostream>
#include <vector>
#include <queue>
#include <cmath>
#include <algorithm>
#include <numeric>
#include <tuple>
#include <climits> // For INT_MAX

using namespace std;

// Optimized penalties based on scoring formula analysis
const int MOVE_PENALTY = 30;  // Reduced since migration penalty is less critical than timeout
const double TIMEOUT_BASE_PENALTY = 1000.0;  // Base penalty for any timeout
const double TIMEOUT_QUADRATIC_FACTOR = 50.0;  // Quadratic growth factor
const double EMERGENCY_THRESHOLD = 0.8;  // When remaining_time/total_time < this, treat as emergency

// --- Data Models ---

struct Server {
    int id;
    int g; // NPU count
    int k; // Speed coefficient
    int m; // Memory size
};

struct NPU {
    int id;
    int server_id;
    int k;
    int m;
    // List of (time, memory_delta) events, kept sorted by time
    vector<pair<int, int>> events;

    void add_task(int start_time, int finish_time, int mem_needed) {
        // Inserts events while maintaining sort order by time.
        // lower_bound finds the first element not less than the given value.
        auto it_start = lower_bound(events.begin(), events.end(), make_pair(start_time, INT_MIN));
        events.insert(it_start, {start_time, mem_needed});

        auto it_finish = lower_bound(events.begin(), events.end(), make_pair(finish_time, INT_MIN));
        events.insert(it_finish, {finish_time, -mem_needed});
    }

    int find_earliest_start_time(int arrival_time, int mem_needed) const {
        if (mem_needed > m) {
            return -1; // Using -1 to represent infinity/failure
        }

        int current_mem_usage = 0; // Changed from long long to int
        for (const auto& event : events) {
            if (event.first <= arrival_time) {
                current_mem_usage += event.second;
            } else {
                break;
            }
        }

        if (current_mem_usage + mem_needed <= m) {
            return arrival_time;
        }

        // If not enough memory, check future event points for released memory
        // Find the first event that happens after arrival_time
        auto it = upper_bound(events.begin(), events.end(), make_pair(arrival_time, INT_MAX));

        for (auto i = it; i != events.end(); ++i) {
            current_mem_usage += i->second;
            if (current_mem_usage + mem_needed <= m) {
                return i->first; // Found a slot starting at this event's time
            }
        }
        return -1; // No suitable slot found
    }
};

struct User {
    int id;
    int s, e, cnt;
    long long samples_left;
    long long next_send_time;
    int last_npu_id = -1;
    // (time, server_id, npu_id_in_server, batch_size)
    vector<tuple<int, int, int, int>> requests;

    // Additional fields for optimization
    double urgency_score = 0.0;
    bool is_emergency = false;
    int estimated_finish_time = -1;

    // Calculate dynamic urgency based on current state
    void update_urgency() {
        if (samples_left <= 0) {
            urgency_score = 0.0;
            is_emergency = false;
            return;
        }

        long long total_time = e - s;
        long long elapsed_time = next_send_time - s;
        long long remaining_time = e - next_send_time;

        if (remaining_time <= 0) {
            urgency_score = 1000000.0;  // Maximum urgency for overdue
            is_emergency = true;
            return;
        }

        // Calculate urgency based on samples density and time pressure
        double time_ratio = (double)elapsed_time / total_time;
        double work_ratio = (double)(cnt - samples_left) / cnt;

        // If work is behind schedule, increase urgency
        double schedule_pressure = (work_ratio < time_ratio) ? (time_ratio - work_ratio) * 10.0 : 0.0;

        // Base urgency from samples per remaining time
        double density_pressure = (double)samples_left / remaining_time;

        urgency_score = density_pressure + schedule_pressure;
        is_emergency = (time_ratio > EMERGENCY_THRESHOLD);
    }
};


// --- Helper Functions ---

// Calculate correct inference time according to the problem statement
// Formula: ceil(Bj / f(Bj)) = ceil(Bj / (ki * sqrt(Bj))) = ceil(sqrt(Bj) / ki)
int calculate_inference_time(int batch_size, int k) {
    if (batch_size <= 0) return 0;
    double sqrt_batch = sqrt(batch_size);
    return static_cast<int>(ceil(sqrt_batch / k));
}

// Mathematical optimization for batch size selection
int find_optimal_batch_size(int max_batch, int samples_left, int k, long long remaining_time, bool is_emergency) {
    int limit = min(max_batch, (int)samples_left);
    if (limit <= 0) return 1;

    // For emergency cases, use more aggressive search
    int search_granularity = is_emergency ? 1 : max(1, limit / 20);

    int best_batch = 1;
    double best_score = -1e9;

    // Mathematical insight: optimal batch size often lies in specific ranges
    // Based on f(B) = k*sqrt(B), the efficiency function has specific properties
    vector<int> key_points;

    // Always test boundary values
    key_points.push_back(1);
    key_points.push_back(limit);

    // Test powers of 2 and their neighbors (often optimal for sqrt functions)
    for (int p = 1; p <= limit; p *= 2) {
        key_points.push_back(p);
        if (p > 1) key_points.push_back(p - 1);
        if (p < limit) key_points.push_back(p + 1);
    }

    // Test perfect squares (optimal for sqrt-based functions)
    for (int i = 1; i * i <= limit; i++) {
        key_points.push_back(i * i);
        if (i * i > 1) key_points.push_back(i * i - 1);
        if (i * i < limit) key_points.push_back(i * i + 1);
    }

    // For non-emergency cases, add systematic sampling
    if (!is_emergency) {
        for (int batch = search_granularity; batch <= limit; batch += search_granularity) {
            key_points.push_back(batch);
        }
    }

    // Remove duplicates and invalid values
    sort(key_points.begin(), key_points.end());
    key_points.erase(unique(key_points.begin(), key_points.end()), key_points.end());

    for (int batch : key_points) {
        if (batch <= 0 || batch > limit) continue;

        int inference_time = calculate_inference_time(batch, k);

        // Advanced scoring function based on mathematical analysis
        double throughput = (double)batch / inference_time;
        double time_efficiency = 1.0;

        if (remaining_time > 0) {
            if (inference_time > remaining_time) {
                // Exponential penalty for exceeding time
                double excess_ratio = (double)(inference_time - remaining_time) / remaining_time;
                time_efficiency = exp(-excess_ratio * 5.0);  // Exponential decay
            } else {
                // Bonus for efficient time usage
                double usage_ratio = (double)inference_time / remaining_time;
                time_efficiency = 1.0 + (1.0 - usage_ratio) * 0.3;  // Up to 30% bonus
            }
        }

        // Emergency cases prioritize any feasible solution
        double emergency_factor = is_emergency ? 2.0 : 1.0;

        double score = throughput * time_efficiency * emergency_factor;

        if (score > best_score) {
            best_score = score;
            best_batch = batch;
        }
    }

    return best_batch;
}

// --- Main Logic ---

int main() {
    // Fast I/O
    ios_base::sync_with_stdio(false);
    cin.tie(NULL);

    // 1. Read Input
    int N;
    cin >> N;
    if (cin.eof()) return 0;

    vector<Server> servers_data(N);
    for (int i = 0; i < N; ++i) {
        servers_data[i].id = i;
        cin >> servers_data[i].g >> servers_data[i].k >> servers_data[i].m;
    }

    int M;
    cin >> M;
    vector<User> users(M);
    for (int i = 0; i < M; ++i) {
        users[i].id = i;
        cin >> users[i].s >> users[i].e >> users[i].cnt;
        users[i].samples_left = users[i].cnt;
        users[i].next_send_time = users[i].s;
    }

    vector<vector<int>> latencies(N, vector<int>(M));
    for (int i = 0; i < N; ++i) {
        for (int j = 0; j < M; ++j) {
            cin >> latencies[i][j];
        }
    }

    int A, B;
    cin >> A >> B;

    // 2. Initialize Models
    vector<NPU> npus;
    int npu_counter = 0;
    for (const auto& server : servers_data) {
        for (int i = 0; i < server.g; ++i) {
            npus.push_back({npu_counter++, server.id, server.k, server.m, {}});
        }
    }

    // 3. Two-phase scheduling: Emergency handling + Regular scheduling

    // Phase 1: Initialize user urgency scores
    for (auto& user : users) {
        user.update_urgency();
    }

    // Phase 2: Separate emergency and regular users
    priority_queue<pair<double, int>> emergency_pq;  // For emergency users
    priority_queue<pair<double, int>> regular_pq;    // For regular users

    for (const auto& user : users) {
        double priority = -user.urgency_score;  // Negative for min-heap behavior

        if (user.is_emergency) {
            emergency_pq.push({priority, user.id});
        } else {
            regular_pq.push({priority, user.id});
        }
    }

    // Process emergency users first, then regular users
    auto process_queue = [&](priority_queue<pair<double, int>>& pq, bool is_emergency_mode) {

        while (!pq.empty()) {
            auto top = pq.top();
            pq.pop();
            int user_id = top.second;
            User& user = users[user_id];

            if (user.samples_left <= 0) continue;

            // Update user urgency before processing
            user.update_urgency();

            long long send_time = user.next_send_time;

            // --- Enhanced Decision Making with multi-objective optimization ---
            double best_cost = 1e9;
            int best_finish_time = -1;
            int best_npu_idx = -1;
            int best_batch_size = -1;

            // Calculate user's remaining time and context
            long long remaining_time = max(0LL, (long long)user.e - send_time);
            double urgency_multiplier = is_emergency_mode ? 10.0 : 1.0;

            // Smart NPU selection with load balancing consideration
            vector<pair<double, int>> npu_candidates;

            for (int i = 0; i < npus.size(); ++i) {
                const auto& npu = npus[i];
                const auto& server = servers_data[npu.server_id];

                if (server.m <= B) continue;
                int max_b_for_npu = (server.m - B) / A;
                if (max_b_for_npu <= 0) continue;

                // Use mathematical optimization for batch size
                int good_batch = find_optimal_batch_size(max_b_for_npu, user.samples_left,
                                                       server.k, remaining_time, user.is_emergency);
                int mem_needed = A * good_batch + B;

                int latency = latencies[server.id][user.id];
                int arrival_time = send_time + latency;

                int start_time = npu.find_earliest_start_time(arrival_time, mem_needed);
                if (start_time == -1) continue;

                // Use correct inference time calculation
                int inference_time = calculate_inference_time(good_batch, server.k);
                int finish_time = start_time + inference_time;

                // Advanced multi-objective cost function
                double cost = 0.0;

                // Primary objective: minimize finish time with urgency weighting
                cost += finish_time * user.urgency_score * urgency_multiplier;

                // Migration penalty (reduced importance for emergency cases)
                if (user.last_npu_id != -1 && npu.id != user.last_npu_id) {
                    double migration_penalty = is_emergency_mode ? MOVE_PENALTY * 0.5 : MOVE_PENALTY;
                    cost += migration_penalty;
                }

                // Timeout penalty based on scoring formula analysis
                if (finish_time > user.e) {
                    double overtime_ratio = (double)(finish_time - user.e) / (user.e - user.s);
                    // Exponential penalty matching the scoring formula h(x) = 2^(-x/100)
                    cost += TIMEOUT_BASE_PENALTY * exp(overtime_ratio * TIMEOUT_QUADRATIC_FACTOR);
                }

                // Resource efficiency bonus
                double throughput = (double)good_batch / inference_time;
                cost -= throughput * 2.0;

                // Load balancing: prefer less loaded NPUs
                int npu_load = npu.events.size();  // Simple load metric
                cost += npu_load * 10.0;

                // Latency consideration
                cost += latency * (is_emergency_mode ? 2.0 : 1.0);

                npu_candidates.push_back({cost, i});
            }

            // Sort candidates by cost and select the best
            if (!npu_candidates.empty()) {
                sort(npu_candidates.begin(), npu_candidates.end());

                best_cost = npu_candidates[0].first;
                best_npu_idx = npu_candidates[0].second;

                // Recalculate best solution details
                const auto& best_npu = npus[best_npu_idx];
                const auto& best_server = servers_data[best_npu.server_id];
                int max_b = (best_server.m - B) / A;
                best_batch_size = find_optimal_batch_size(max_b, user.samples_left,
                                                        best_server.k, remaining_time, user.is_emergency);
                int latency = latencies[best_server.id][user.id];
                int arrival_time = send_time + latency;
                int start_time = best_npu.find_earliest_start_time(arrival_time, A * best_batch_size + B);
                int inference_time = calculate_inference_time(best_batch_size, best_server.k);
                best_finish_time = start_time + inference_time;
            }

            // --- Commit to the best decision ---
            if (best_npu_idx != -1) {
                NPU& chosen_npu = npus[best_npu_idx];
                int batch = best_batch_size;

                int mem_needed = A * batch + B;
                int latency = latencies[chosen_npu.server_id][user.id];
                int server_k = servers_data[chosen_npu.server_id].k;
                int inference_time = calculate_inference_time(batch, server_k);
                int start_time = best_finish_time - inference_time;

                chosen_npu.add_task(start_time, best_finish_time, mem_needed);

                int npu_id_in_server;
                int base_npu_id = 0;
                for(int i = 0; i < chosen_npu.server_id; ++i) {
                    base_npu_id += servers_data[i].g;
                }
                npu_id_in_server = chosen_npu.id - base_npu_id + 1;

                user.requests.emplace_back(send_time, chosen_npu.server_id + 1, npu_id_in_server, batch);

                user.samples_left -= batch;
                user.next_send_time = send_time + latency + 1;
                user.last_npu_id = chosen_npu.id;
                user.estimated_finish_time = best_finish_time;

                if (user.samples_left > 0) {
                    // Update urgency and re-queue
                    user.update_urgency();
                    double priority = -user.urgency_score;

                    // Re-categorize user if urgency changed
                    if (user.is_emergency && !is_emergency_mode) {
                        emergency_pq.push({priority, user.id});
                    } else if (!user.is_emergency && is_emergency_mode) {
                        regular_pq.push({priority, user.id});
                    } else {
                        pq.push({priority, user.id});
                    }
                }
            }
        }
    };

    // Execute two-phase scheduling
    process_queue(emergency_pq, true);   // Process emergency users first
    process_queue(regular_pq, false);    // Then process regular users

    // 4. Print Output
    for (auto& user : users) {
        sort(user.requests.begin(), user.requests.end());
        cout << user.requests.size() << "\n";
        bool first = true;
        for (const auto& req : user.requests) {
            if (!first) {
                cout << " ";
            }
            cout << get<0>(req) << " " << get<1>(req) << " "
                 << get<2>(req) << " " << get<3>(req);
            first = false;
        }
        cout << "\n";
    }

    return 0;
} 